# Pecco 宠物用品官网部署指南

## 环境配置

### 1. 开发环境（本地）

#### 步骤 1: 设置开发环境

```bash
# 手动复制环境文件
cp config/env.dev .env
```

#### 步骤 2: 启动开发环境

```bash
# 启动开发环境
docker-compose up -d --build

# 执行迁移
docker-compose exec web python pecco_backend/manage.py migrate

# 创建超级用户
docker-compose exec web python pecco_backend/manage.py createsuperuser
```

#### 步骤 3: 开发时的环境管理

```bash
# 切换到开发环境
cp config/env.dev .env

# 切换到生产环境（本地测试用）
cp config/env.prod .env
```

### 2. 生产环境（服务器）

#### 步骤 1: 准备环境变量

在服务器上创建 `env.prod` 文件：

```bash
# 复制示例文件
cp env.prod.example env.prod

# 编辑配置文件
nano env.prod
```

配置内容示例：
```bash
# Git 仓库配置
GIT_REPO=https://gitee.com/your-username/pecco-pet-shop.git
GIT_BRANCH=main

# Django 设置
SECRET_KEY=your-production-secret-key-here
DEBUG=False
ALLOWED_HOSTS=your-domain.com,www.your-domain.com

# 数据库设置
MYSQL_DATABASE=pecco
MYSQL_ROOT_PASSWORD=your-production-db-password
```

#### 步骤 2: 首次部署

```bash
# 给部署脚本执行权限
chmod +x deploy.sh

# 执行部署
./deploy.sh
```

#### 步骤 3: 更新部署

当代码有更新时，只需要：

1. 将代码推送到 Gitee 仓库
2. 在服务器上运行：`./deploy.sh`

## 文件说明

### 核心文件
- `docker-compose.yml` - 开发环境配置
- `docker-compose.prod.yml` - 生产环境配置
- `Dockerfile` - 开发环境镜像构建
- `Dockerfile.prod` - 生产环境镜像构建（从 Git 拉取代码）

### 环境配置
- `pecco_backend/settings_base.py` - 基础设置（通用配置）
- `pecco_backend/settings_dev.py` - 开发环境特定设置
- `pecco_backend/settings_prod.py` - 生产环境特定设置
- `pecco_backend/settings.py` - 主设置文件（根据环境变量选择配置）

### 环境变量文件
- `pecco_backend/env.dev` - 开发环境变量（不提交到 Git）
- `pecco_backend/env.example` - 环境变量示例
- `env.prod` - 生产环境变量（不提交到 Git）

### 管理脚本
- `deploy.sh` - 生产环境部署脚本

## 注意事项

1. **安全性**：`env.prod` 文件包含敏感信息，不要提交到 Git 仓库
2. **数据库备份**：生产环境建议定期备份数据库
3. **域名配置**：记得在 `ALLOWED_HOSTS` 中配置你的域名
4. **SSL 证书**：生产环境建议配置 HTTPS

## 故障排除

### 常见问题

1. **Git 仓库访问失败**
   - 检查 `GIT_REPO` 地址是否正确
   - 确认仓库是公开的或配置了访问权限

2. **数据库连接失败**
   - 检查 `MYSQL_ROOT_PASSWORD` 是否正确
   - 确认数据库容器已启动

3. **静态文件无法访问**
   - 运行 `docker-compose -f docker-compose.prod.yml exec web python pecco_backend/manage.py collectstatic --noinput`

4. **权限问题**
   - 确保 `deploy.sh` 有执行权限：`chmod +x deploy.sh`
