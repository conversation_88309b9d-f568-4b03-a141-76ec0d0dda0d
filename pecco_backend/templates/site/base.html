{% load static %}
{% load multilang_tags %}
<!doctype html>
{% if locale == 'zh' %}<html lang="zh">{% elif locale == 'nl' %}<html lang="nl">{% elif locale == 'fr' %}<html lang="fr">{% elif locale == 'de' %}<html lang="de">{% else %}<html lang="en">{% endif %}
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>pecco - {% get_common_text 'slogan' locale %}</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Jost:ital,wght@0,100..900;1,100..900&family=Dancing+Script:wght@400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="{% static 'pecco_site/css/site.css' %}">
</head>
<body>
<header class="topbar">
  <div class="left">
    <a href="/"><img src="{% static 'pecco_site/img/logo_pecco.svg' %}" class="logo" alt="PECCO logo"/></a>
    <div class="tagline-script">
      <span class="script-line1">Happy Paws,</span>
      <span class="script-line2">Happy Days</span>
    </div>
  </div>

  <!-- Mobile tagline in center of topbar -->
  <div class="mobile-topbar-tagline">
    <span class="mobile-topbar-script-line1">Happy Paws,</span>
    <span class="mobile-topbar-script-line2">Happy Days</span>
  </div>

  <div class="right">
    <nav class="main-nav">
      {% for n in nav %}
        <a href="{{ n.url }}"{% if n.is_active %} class="active"{% endif %}>{{ n.label }}</a>
      {% endfor %}
    </nav>
    <div class="search-container">
      <div class="search-box">
        <input type="text" class="search-input" placeholder="{% if locale == 'zh' %}搜索产品...{% elif locale == 'nl' %}Zoek producten...{% elif locale == 'fr' %}Rechercher des produits...{% elif locale == 'de' %}Produkte suchen...{% else %}Search products...{% endif %}" autocomplete="off">
        <svg class="search-icon" width="18" height="18" viewBox="0 0 24 24" fill="none">
          <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
          <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2"/>
        </svg>
        <div class="search-results"></div>
      </div>
    </div>
    <div class="language-selector">
      <button class="language-toggle" aria-label="Select language">
        <span class="current-lang">
          {% if locale == 'en' %}EN{% elif locale == 'zh' %}中文{% elif locale == 'nl' %}NL{% elif locale == 'fr' %}FR{% elif locale == 'de' %}DE{% endif %}
        </span>
        <svg class="dropdown-icon" width="12" height="8" viewBox="0 0 12 8" fill="none">
          <path d="M1 1.5L6 6.5L11 1.5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </button>
      <div class="language-dropdown">
        <a href="/lang/en" class="lang-option{% if locale == 'en' %} active{% endif %}">
          <span class="lang-code">EN</span>
          <span class="lang-name">English</span>
        </a>
        <a href="/lang/zh" class="lang-option{% if locale == 'zh' %} active{% endif %}">
          <span class="lang-code">中文</span>
          <span class="lang-name">中文</span>
        </a>
        <a href="/lang/nl" class="lang-option{% if locale == 'nl' %} active{% endif %}">
          <span class="lang-code">NL</span>
          <span class="lang-name">Nederlands</span>
        </a>
        <a href="/lang/fr" class="lang-option{% if locale == 'fr' %} active{% endif %}">
          <span class="lang-code">FR</span>
          <span class="lang-name">Français</span>
        </a>
        <a href="/lang/de" class="lang-option{% if locale == 'de' %} active{% endif %}">
          <span class="lang-code">DE</span>
          <span class="lang-name">Deutsch</span>
        </a>
      </div>
    </div>
  </div>

  <!-- Mobile Menu Toggle Button -->
  <button class="mobile-menu-toggle" aria-label="Toggle mobile menu">
    <span></span>
    <span></span>
    <span></span>
  </button>
</header>

<!-- Mobile Navigation -->
<div class="mobile-nav">
  <div class="mobile-nav-header">
    <button class="mobile-nav-close" aria-label="Close mobile menu">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
        <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </button>
  </div>
  <div class="mobile-nav-content">
    {% for n in nav %}
      <a href="{{ n.url }}"{% if n.is_active %} class="active"{% endif %}>{{ n.label }}</a>
    {% endfor %}
    <div class="mobile-lang-switcher">
      <div class="mobile-language-selector">
        <div class="mobile-lang-options">
          <a href="/lang/en" class="mobile-lang-option{% if locale == 'en' %} active{% endif %}">
            <span class="mobile-lang-code">EN</span>
            <span class="mobile-lang-name">English</span>
          </a>
          <a href="/lang/zh" class="mobile-lang-option{% if locale == 'zh' %} active{% endif %}">
            <span class="mobile-lang-code">中文</span>
            <span class="mobile-lang-name">中文</span>
          </a>
          <a href="/lang/nl" class="mobile-lang-option{% if locale == 'nl' %} active{% endif %}">
            <span class="mobile-lang-code">NL</span>
            <span class="mobile-lang-name">Nederlands</span>
          </a>
          <a href="/lang/fr" class="mobile-lang-option{% if locale == 'fr' %} active{% endif %}">
            <span class="mobile-lang-code">FR</span>
            <span class="mobile-lang-name">Français</span>
          </a>
          <a href="/lang/de" class="mobile-lang-option{% if locale == 'de' %} active{% endif %}">
            <span class="mobile-lang-code">DE</span>
            <span class="mobile-lang-name">Deutsch</span>
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

{% if messages %}
<div class="flash-area">
  {% for m in messages %}
    <div class="flash {{ m.tags }}">{{ m }}</div>
  {% endfor %}
</div>
{% endif %}
{% if breadcrumb %}
  <div class="breadcrumb">
    <a href="/">{% get_common_text 'home_breadcrumb' locale %}</a>
    {% for bc in breadcrumb %}
      <span class="sep">›</span>
      {% if bc.url %}<a href="{{ bc.url }}">{{ bc.label }}</a>{% else %}<span>{{ bc.label }}</span>{% endif %}
    {% endfor %}
  </div>
{% endif %}
<main class="page">
  {% block content %}{% endblock %}
</main>

<!-- Footer -->
<footer class="footer">
  <div class="footer-container">
    <div class="footer-content">
      <div class="footer-column footer-company">
        <div class="company-info">
          <div class="company-name">PECCO PETS</div>
          <div class="company-address">
            Douwencamp 42<br>
            3861LE Nijkerk<br>
            {% if locale == 'zh' %}荷兰{% elif locale == 'nl' %}Nederland{% elif locale == 'fr' %}Pays-Bas{% elif locale == 'de' %}Niederlande{% else %}Netherlands{% endif %}<br>
            <EMAIL>
          </div>
        </div>
      </div>
      <div class="footer-column footer-newsletter">
        <div class="newsletter-section">
          <h3 class="newsletter-title">
            {% if locale == 'zh' %}订阅我们的邮件{% elif locale == 'nl' %}Abonneer op onze nieuwsbrief{% elif locale == 'fr' %}Abonnez-vous à notre newsletter{% elif locale == 'de' %}Abonnieren Sie unseren Newsletter{% else %}Subscribe To Our Newsletter{% endif %}
          </h3>
          <p class="newsletter-subtitle">
            {% if locale == 'zh' %}获取新产品和即将到来的销售的最新更新{% elif locale == 'nl' %}Ontvang de laatste updates over nieuwe producten en aankomende verkopen{% elif locale == 'fr' %}Obtenez les dernières mises à jour sur les nouveaux produits et les ventes à venir{% elif locale == 'de' %}Erhalten Sie die neuesten Updates zu neuen Produkten und bevorstehenden Verkäufen{% else %}Get the latest updates on new products and upcoming sales{% endif %}
          </p>
          <form class="newsletter-form" id="newsletter-form">
            {% csrf_token %}
            <div class="newsletter-input-group">
              <input type="email" name="email" class="newsletter-input" placeholder="{% if locale == 'zh' %}输入您的邮箱地址{% elif locale == 'nl' %}voer uw e-mailadres in{% elif locale == 'fr' %}entrez votre adresse e-mail{% elif locale == 'de' %}geben Sie Ihre E-Mail-Adresse ein{% else %}enter your email address{% endif %}" required>
              <button type="submit" class="newsletter-btn">
                {% if locale == 'zh' %}订阅{% elif locale == 'nl' %}Abonneren{% elif locale == 'fr' %}S'abonner{% elif locale == 'de' %}Abonnieren{% else %}Subscribe{% endif %}
              </button>
            </div>
          </form>
          <div class="newsletter-message" id="newsletter-message"></div>
        </div>
      </div>
      <div class="footer-column footer-nav">
        <ul class="footer-links">
          <li><a href="/about/">{% get_footer_link_text 'about' locale %}</a></li>
          <li><a href="/products/">{% get_footer_link_text 'products' locale %}</a></li>
          <li><a href="/contact/">{% get_footer_link_text 'reseller' locale %}</a></li>
          <li><a href="/privacy/">{% get_footer_link_text 'privacy' locale %}</a></li>
          <li><a href="/contact/">{% get_footer_link_text 'contact' locale %}</a></li>
        </ul>
      </div>
    </div>
    <div class="footer-bottom">
      <div class="copyright">
        © {% now 'Y' %} {% if locale == 'zh' %}由 PECCO 提供{% elif locale == 'nl' %}Door PECCO{% elif locale == 'fr' %}Par PECCO{% elif locale == 'de' %}Von PECCO{% else %}By PECCO{% endif %}
      </div>
      <div class="icp-info">
        <a href="https://beian.miit.gov.cn/" target="_blank" rel="noopener noreferrer">湘ICP备2025135184号</a>
      </div>
    </div>
  </div>
</footer>
<script src="{% static 'pecco_site/js/lazy-loading.js' %}"></script>
<script src="{% static 'pecco_site/js/site.js' %}"></script>

<!-- Search functionality -->
<script>
document.addEventListener('DOMContentLoaded', function() {
  const searchInput = document.querySelector('.search-input');
  const searchResults = document.querySelector('.search-results');
  let searchTimeout;
  let currentHighlight = -1;

  if (!searchInput || !searchResults) return;

  // Search function
  function performSearch(query) {
    if (query.length < 2) {
      searchResults.classList.remove('show');
      return;
    }

    // Get current locale from the page
    const currentLocale = '{{ locale }}';

    fetch(`/api/search/?q=${encodeURIComponent(query)}&locale=${currentLocale}`)
      .then(response => response.json())
      .then(data => {
        displayResults(data.results);
      })
      .catch(error => {
        console.error('Search error:', error);
        searchResults.classList.remove('show');
      });
  }

  // Display search results
  function displayResults(results) {
    if (results.length === 0) {
      searchResults.innerHTML = '<div class="search-no-results">{% if locale == "zh" %}未找到相关产品{% elif locale == "nl" %}Geen producten gevonden{% elif locale == "fr" %}Aucun produit trouvé{% elif locale == "de" %}Keine Produkte gefunden{% else %}No products found{% endif %}</div>';
      searchResults.classList.add('show');
      return;
    }

    const html = results.map((item, index) => `
      <a href="${item.url}" class="search-result-item" data-index="${index}">
        <img src="${item.cover}" alt="${item.name}" class="search-result-image" onerror="this.style.display='none'">
        <div class="search-result-content">
          <h4 class="search-result-name">${item.name}</h4>
          <p class="search-result-desc">${item.short_desc}</p>
          ${item.tags.length > 0 ? `<div class="search-result-tags">${item.tags.map(tag => `<span class="search-result-tag tag-${tag}">${tag}</span>`).join('')}</div>` : ''}
        </div>
      </a>
    `).join('');

    searchResults.innerHTML = html;
    searchResults.classList.add('show');
    currentHighlight = -1;
  }

  // Handle keyboard navigation
  function handleKeyNavigation(e) {
    const items = searchResults.querySelectorAll('.search-result-item');

    if (e.key === 'ArrowDown') {
      e.preventDefault();
      currentHighlight = Math.min(currentHighlight + 1, items.length - 1);
      updateHighlight(items);
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      currentHighlight = Math.max(currentHighlight - 1, -1);
      updateHighlight(items);
    } else if (e.key === 'Enter') {
      e.preventDefault();
      if (currentHighlight >= 0 && items[currentHighlight]) {
        items[currentHighlight].click();
      }
    } else if (e.key === 'Escape') {
      searchResults.classList.remove('show');
      searchInput.blur();
    }
  }

  // Update highlight
  function updateHighlight(items) {
    items.forEach((item, index) => {
      item.classList.toggle('highlighted', index === currentHighlight);
    });
  }

  // Event listeners
  searchInput.addEventListener('input', function(e) {
    clearTimeout(searchTimeout);
    const query = e.target.value.trim();

    searchTimeout = setTimeout(() => {
      performSearch(query);
    }, 300);
  });

  searchInput.addEventListener('keydown', handleKeyNavigation);

  searchInput.addEventListener('focus', function() {
    if (this.value.trim().length >= 2) {
      searchResults.classList.add('show');
    }
  });

  // Close search results when clicking outside
  document.addEventListener('click', function(e) {
    if (!e.target.closest('.search-container')) {
      searchResults.classList.remove('show');
    }
  });
});

// Newsletter subscription
document.addEventListener('DOMContentLoaded', function() {
  const newsletterForm = document.getElementById('newsletter-form');
  const newsletterMessage = document.getElementById('newsletter-message');

  if (newsletterForm) {
    newsletterForm.addEventListener('submit', function(e) {
      e.preventDefault();

      const formData = new FormData(newsletterForm);
      const submitBtn = newsletterForm.querySelector('.newsletter-btn');
      const originalText = submitBtn.textContent;

      // Show loading state
      submitBtn.textContent = '{% if locale == "zh" %}提交中...{% elif locale == "nl" %}Verzenden...{% elif locale == "fr" %}Envoi...{% elif locale == "de" %}Senden...{% else %}Submitting...{% endif %}';
      submitBtn.disabled = true;

      fetch('/newsletter-subscribe/', {
        method: 'POST',
        body: formData,
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
        }
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          newsletterMessage.innerHTML = '<div class="success-message">' + data.message + '</div>';
          newsletterForm.reset();
        } else {
          newsletterMessage.innerHTML = '<div class="error-message">' + data.message + '</div>';
        }
      })
      .catch(error => {
        newsletterMessage.innerHTML = '<div class="error-message">{% if locale == "zh" %}提交失败，请稍后重试{% elif locale == "nl" %}Verzending mislukt, probeer het later opnieuw{% elif locale == "fr" %}Échec de l\'envoi, veuillez réessayer plus tard{% elif locale == "de" %}Übermittlung fehlgeschlagen, bitte versuchen Sie es später erneut{% else %}Submission failed, please try again later{% endif %}</div>';
      })
      .finally(() => {
        // Reset button state
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;

        // Clear message after 5 seconds
        setTimeout(() => {
          newsletterMessage.innerHTML = '';
        }, 5000);
      });
    });
  }
});
</script>
</body>
</html>

